import HeaderSVG from '@/assets/home/<USER>'
import Exit from '@/assets/home/<USER>'
import RoundIconButton from '@/components/common/RoundIconButton'
import { AspectRatio } from '@/components/ui/aspect-ratio'
import Image from 'next/image'
import { useGameStore } from '@/stores/useGameStore'

interface HeaderProps {
  onOpenGiftList: () => void
}

export const Header: React.FC<HeaderProps> = ({ onOpenGiftList }) => {
  const { turns, gifts } = useGameStore()
  return (
    <div className='absolute top-0 left-0 z-20 h-[16%] w-full'>
      <HeaderSVG className='drop-shadow-down-2xl absolute h-full w-full' />

      <div className='relative z-30 flex h-full w-full items-center justify-between px-[2%]'>
        {/* Exit */}
        <RoundIconButton
          icon={<Exit className='z-20 h-[50%] w-[50%]' />}
          onClick={() => console.log('User exits the app')}
        />

        {/* Turns & Gifts */}
        <div className='flex w-full items-center justify-end gap-[6%]'>
          <div className='w-[29%] translate-y-[5%]'>
            <AspectRatio ratio={363 / 173}>
              <div
                className='relative flex h-full w-full cursor-pointer items-center justify-center'
                onClick={() => console.log('User opens Turns')}
              >
                <Image src='/home/<USER>' alt='Turns Button' fill />
                <span className='absolute z-20 ml-[30%] text-[18.54px] text-[#FF5500]'>{turns}</span>
              </div>
            </AspectRatio>
          </div>

          {/* Gifts */}
          <div className='w-[29.5%]'>
            <AspectRatio ratio={370 / 190}>
              <div
                className='relative flex h-full w-full cursor-pointer items-center justify-center'
                onClick={onOpenGiftList} // 🔹 open gift drawer
              >
                <Image src='/home/<USER>' alt='Gifts Button' fill />
                <span className='absolute z-20 mt-[5%] ml-[35%] text-[18.54px] text-[#FF5500]'>{gifts}</span>
              </div>
            </AspectRatio>
          </div>
        </div>
      </div>
    </div>
  )
}
