// gameBoard.tsx
'use client'
import React, { useEffect } from 'react'
import { Point, BOARD_WIDTH, BOARD_HEIGHT, CELL_WIDTH, CELL_HEIGHT, GAP, WrongPair } from '@/lib/constants'
import { findCentre } from '@/lib/gameUtils'
import { useGameStore } from '@/hooks/gameScreen/useGameBoard'
import { useGameInteractions } from '@/hooks/gameScreen/useGameBoard'
import Image from 'next/image'

interface GamePieceProps {
  value: number
  isSelected: boolean
  isHinted: boolean
  isWrong: boolean
  onClick: () => void
}

function GamePiece({ value, isSelected, isHinted, isWrong, onClick }: GamePieceProps) {
  const empty = value === 0
  const pieceImg = `/game/piece${value}.png`
  const scaleClass = value === 8 || value === 9 ? 'scale-125' : 'scale-100'

  return (
    <div
      onClick={empty ? undefined : onClick}
      className={`relative flex items-center justify-center rounded transition-all ${
        empty ? '' : 'piece-shadow cursor-pointer'
      }`}
      style={{
        width: CELL_WIDTH,
        height: CELL_HEIGHT,
        transform: isWrong ? 'translateZ(0)' : undefined,
        animation: isWrong ? 'flicker 160ms linear 0s 3' : undefined
      }}
    >
      {!empty && (
        <>
          <Image src='/game/pieceBG.png' alt='Piece background' fill sizes={`${CELL_WIDTH}px`} quality={100} />
          <div className='relative z-10 flex h-[95%] w-[95%] items-center justify-center'>
            <Image
              src={pieceImg}
              alt={`Piece ${value}`}
              fill
              className={`object-contain ${scaleClass}`}
              quality={100}
            />
          </div>
        </>
      )}

      {/* Selected overlay */}
      {isSelected && (
        <div
          className='absolute inset-0 rounded'
          style={{
            border: '2px solid rgba(255,255,255,0.7)',
            boxShadow: '0 0 10px rgba(255,255,255,0.7)'
          }}
        />
      )}

      {/* Hinted overlay */}
      {isHinted && (
        <div
          className='absolute inset-0 rounded'
          style={{
            border: '2px solid #FFD700',
            boxShadow: '0 0 10px #FFD700'
          }}
        />
      )}
    </div>
  )
}

interface PathOverlayProps {
  path: Point[]
  showPath: boolean
  svgWidth: number
  svgHeight: number
}

function PathOverlay({ path, showPath, svgWidth, svgHeight }: PathOverlayProps) {
  if (!showPath || !path || path.length < 2) return null
  const margin = 10
  const clampToSvgBounds = (x: number, y: number) => ({
    x: Math.max(-margin, Math.min(svgWidth + margin, x)),
    y: Math.max(-margin, Math.min(svgHeight + margin, y))
  })
  const [start, ...rest] = path
  const startCenter = findCentre(start.x, start.y)
  const clampedStart = clampToSvgBounds(startCenter.x, startCenter.y)
  let d = `M ${clampedStart.x} ${clampedStart.y}`
  for (const p of rest) {
    const { x: cx, y: cy } = findCentre(p.x, p.y)
    const clampedPoint = clampToSvgBounds(cx, cy)
    d += ` L ${clampedPoint.x} ${clampedPoint.y}`
  }
  return (
    <svg
      className='pointer-events-none absolute inset-0 z-30'
      width={svgWidth}
      height={svgHeight}
      style={{ overflow: 'visible' }}
    >
      <path
        d={d}
        stroke='#FFD700'
        strokeWidth='6'
        strokeLinecap='round'
        strokeLinejoin='round'
        fill='none'
        opacity='0.9'
      />
    </svg>
  )
}

interface GameBoardProps {
  board: number[][]
  highlighted: Point | null
  wrongPair: WrongPair | null
  hintedPair: WrongPair | null
  onPieceClick: (col: number, row: number) => void
}

function GameBoard({ board, highlighted, wrongPair, hintedPair, onPieceClick }: GameBoardProps) {
  const isSelected = (col: number, row: number) => highlighted?.x === col && highlighted?.y === row
  const isWrong = (c: number, r: number) =>
    !!wrongPair && ((wrongPair.a.x === c && wrongPair.a.y === r) || (wrongPair.b.x === c && wrongPair.b.y === r))
  const isHinted = (c: number, r: number) =>
    !!hintedPair && ((hintedPair.a.x === c && hintedPair.a.y === r) || (hintedPair.b.x === c && hintedPair.b.y === r))
  return (
    <div
      className='relative z-40 grid'
      style={{
        gridTemplateColumns: `repeat(${BOARD_WIDTH}, ${CELL_WIDTH}px)`,
        gridTemplateRows: `repeat(${BOARD_HEIGHT}, ${CELL_HEIGHT}px)`,
        gap: `${GAP}px`
      }}
    >
      {board.map((row, r) =>
        row.map((cell, c) => (
          <GamePiece
            key={`${r}-${c}`}
            value={cell}
            isSelected={isSelected(c, r)}
            isHinted={isHinted(c, r)}
            isWrong={isWrong(c, r)}
            onClick={() => onPieceClick(c, r)}
          />
        ))
      )}
    </div>
  )
}

interface PikachuGameProps {
  stage: number
}

const PikachuGame: React.FC<PikachuGameProps> = ({ stage }) => {
  const { gameState, startNewGame, updateGameState } = useGameStore()
  const { highlighted, showPath, wrongPair, handlePieceClick } = useGameInteractions(gameState, updateGameState, stage)

  useEffect(() => {
    startNewGame(stage)
  }, [startNewGame, stage])

  const svgWidth = BOARD_WIDTH * CELL_WIDTH + (BOARD_WIDTH - 1) * GAP
  const svgHeight = BOARD_HEIGHT * CELL_HEIGHT + (BOARD_HEIGHT - 1) * GAP

  return (
    <div className='relative flex items-center justify-center'>
      <div
        className='relative box-border flex items-center justify-center'
        style={{ width: svgWidth, height: svgHeight }}
      >
        <PathOverlay path={gameState.path} showPath={showPath} svgWidth={svgWidth} svgHeight={svgHeight} />
        <GameBoard
          board={gameState.board}
          highlighted={highlighted}
          wrongPair={wrongPair}
          hintedPair={gameState.hintedPair}
          onPieceClick={handlePieceClick}
        />
      </div>
    </div>
  )
}

export default PikachuGame
