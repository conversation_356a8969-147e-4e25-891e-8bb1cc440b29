// useGameBoard.ts
import { useState, useCallback } from 'react'
import { findPath, hasValidMoves, getRandomMatchablePair } from '@/lib/pathFindingUtils'
import { Point, GameState, WrongPair } from '@/lib/constants'
import { generateBoard, fixMatrix, getFixTypeFromStage } from '@/lib/gameUtils'
import { create } from 'zustand'

interface GameStore {
  gameState: GameState
  startNewGame: (stage?: number) => void
  updateGameState: (updater: (prev: GameState) => GameState) => void
}

export const useGameStore = create<GameStore>((set) => ({
  gameState: {
    board: [],
    selectedPiece: null,
    level: 1,
    lives: 10,
    isGameOver: false,
    isWon: false,
    path: [],
    hintCount: 3,
    hintedPair: null
  },
  startNewGame: (stage = 1) =>
    set({
      gameState: {
        board: generateBoard(),
        selectedPiece: null,
        level: stage,
        lives: 10,
        isGameOver: false,
        isWon: false,
        path: [],
        hintCount: 3,
        hintedPair: null
      }
    }),
  updateGameState: (updater) => set((state) => ({ gameState: updater(state.gameState) }))
}))

export function useGameInteractions(
  gameState: GameState,
  updateGameState: (updater: (prev: GameState) => GameState) => void,
  stage: number
) {
  const [highlighted, setHighlighted] = useState<Point | null>(null)
  const [showPath, setShowPath] = useState(false)
  const [wrongPair, setWrongPair] = useState<WrongPair | null>(null)

  const handleUseHint = useCallback(() => {
    if (gameState.hintCount <= 0 || gameState.hintedPair) return
    const pair = getRandomMatchablePair(gameState.board)
    if (!pair) return
    updateGameState((prev) => ({ ...prev, hintCount: prev.hintCount - 1, hintedPair: pair }))
  }, [gameState.hintCount, gameState.hintedPair, gameState.board, updateGameState])

  const handlePieceClick = useCallback(
    (col: number, row: number) => {
      if (gameState.isGameOver || gameState.isWon) return
      if (gameState.board[row][col] === 0) return

      // Case 1: no selected piece yet
      if (!gameState.selectedPiece) {
        setHighlighted({ x: col, y: row })
        updateGameState((prev) => ({ ...prev, selectedPiece: { x: col, y: row }, path: [] }))
        setShowPath(false)
        return
      }

      // Case 2: same piece clicked → clear
      if (gameState.selectedPiece.x === col && gameState.selectedPiece.y === row) {
        setHighlighted(null)
        updateGameState((prev) => ({ ...prev, selectedPiece: null, path: [] }))
        setShowPath(false)
        return
      }

      // Case 3: check path
      const path = findPath(gameState.board, gameState.selectedPiece, { x: col, y: row })
      if (!path) {
        // wrong pair → flicker both pieces then clear selection
        const a = gameState.selectedPiece
        const b = { x: col, y: row }
        setWrongPair({ a, b })
        setHighlighted(null)
        setShowPath(false)
        setTimeout(() => {
          setWrongPair(null)
          updateGameState((prev) => ({ ...prev, selectedPiece: null, path: [] }))
        }, 500)
        return
      }

      // correct pair - hide ring immediately and show path
      setHighlighted(null)
      setShowPath(true)

      const newBoard = gameState.board.map((r) => [...r])
      newBoard[gameState.selectedPiece.y][gameState.selectedPiece.x] = 0
      newBoard[row][col] = 0

      updateGameState((prev) => ({
        ...prev,
        board: newBoard,
        selectedPiece: null,
        hintedPair: null,
        path
      }))

      setTimeout(() => {
        setShowPath(false)

        const fixType = getFixTypeFromStage(stage)
        const movedBoard = fixMatrix(newBoard, fixType)
        const remaining = movedBoard.flat().filter((x) => x !== 0).length

        if (remaining === 0) {
          // Stage completed - set win state
          updateGameState((cur) => ({
            ...cur,
            path: [],
            board: movedBoard,
            hintedPair: null,
            isWon: true
          }))
        } else {
          // Check if there are valid moves
          const hasMovesAvailable = hasValidMoves(movedBoard)

          if (!hasMovesAvailable) {
            // No valid moves - lose a life and shuffle or end game
            const newLives = gameState.lives - 1
            const gameOver = newLives <= 0

            if (gameOver) {
              updateGameState((cur) => ({
                ...cur,
                path: [],
                board: movedBoard,
                hintedPair: null,
                lives: newLives,
                isGameOver: true
              }))
            } else {
              // Shuffle remaining pieces
              const pieces = movedBoard.flat().filter((x) => x !== 0)
              for (let i = pieces.length - 1; i > 0; i--) {
                const j = Math.floor(Math.random() * (i + 1))
                ;[pieces[i], pieces[j]] = [pieces[j], pieces[i]]
              }

              let idx = 0
              const shuffledBoard = movedBoard.map((row) => row.map((cell) => (cell !== 0 ? pieces[idx++] : 0)))

              updateGameState((cur) => ({
                ...cur,
                path: [],
                board: shuffledBoard,
                hintedPair: null,
                lives: newLives
              }))
            }
          } else {
            // Valid moves available - continue game
            updateGameState((cur) => ({
              ...cur,
              path: [],
              board: movedBoard,
              hintedPair: null
            }))
          }
        }
      }, 500)
    },
    [gameState, updateGameState, stage]
  )

  return {
    highlighted,
    showPath,
    wrongPair,
    handlePieceClick,
    handleUseHint,
    setHighlighted,
    setShowPath,
    setWrongPair
  }
}
