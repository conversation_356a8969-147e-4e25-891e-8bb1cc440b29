// src/components/dialog/ShareDialog.tsx
'use client'

import { Dialog, DialogContent } from '@/components/ui/dialog'
import { AspectRatio } from '@/components/ui/aspect-ratio'
import Image from 'next/image'
import MediumerOrangeButton from '@/assets/drawer/mediumerOrangeButton.svg'
import MediumDisabledButton from '@/assets/drawer/mediumDisabledButton.svg'
import { Avatar, AvatarImage, AvatarFallback } from '@/components/ui/avatar'

interface ShareDialogProps {
  open: boolean
  onClose: () => void
}

const ShareDialog: React.FC<ShareDialogProps> = ({ open, onClose }) => {
  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className='h-full border-none bg-transparent p-0 shadow-none'>
        {/* Top Bar */}
        <div className='absolute top-0 z-10 flex h-[12.3%] w-full items-end justify-center'>
          <span className='font-bdstreet text-border-orange text-[32px]'>Chia sẻ cộng đồng</span>
        </div>

        {/* Main Center Content */}
        <div className='flex h-full w-full items-center justify-center'>
          <AspectRatio ratio={1560 / 2184} className='relative flex justify-center'>
            <Image src='/share/shareBG.png' alt='Share' fill quality={100} />

            {/* Inner AspectRatio centered vertically */}
            <div className='absolute inset-0 flex items-center justify-center'>
              <AspectRatio ratio={1560 / 1076} className='relative shadow-[3px_4px_150px_15px_rgba(255,255,112,0.3)]'>
                <Image src='/share/winShare.webp' alt='Share' fill quality={100} />
                <div className='mx-auto mt-[5%] w-[20%]'>
                  <AspectRatio ratio={1}>
                    <div className='relative h-full w-full rounded-full bg-gradient-to-b from-[#FFFF70] to-[#F6B936] to-50% p-[3px]'>
                      {/* Inner circle for the avatar itself */}
                      <div className='h-full w-full rounded-full'>
                        <Avatar className='h-full w-full'>
                          <AvatarImage
                            src='https://static.wikia.nocookie.net/floptok/images/b/b8/Hot-girl-linda-la-ai-2.jpg'
                            alt='CN'
                          />
                          <AvatarFallback>CN</AvatarFallback>
                        </Avatar>
                      </div>
                    </div>
                  </AspectRatio>
                </div>
              </AspectRatio>
            </div>
          </AspectRatio>
        </div>

        {/* Bottom Container */}
        <div className='absolute bottom-0 z-10 flex h-[22%] w-full flex-col items-center'>
          {/* Top Inner Div */}
          <div className='h-[39.2%] w-[92.5%] rounded-3xl border border-white/30 bg-white/5 text-white backdrop-blur-xs'></div>

          {/* Bottom Row */}
          <div className='flex h-[60.8%] w-full items-center justify-center space-x-[10%]'>
            <div className='relative w-[35%]'>
              <AspectRatio ratio={137 / 48} className='relative'>
                <MediumerOrangeButton className='h-full w-full' />
              </AspectRatio>
              <span className='font-bdstreet text-shadow-orange-btn absolute inset-0 flex items-center justify-center text-[20px] font-bold text-white'>
                Đóng
              </span>
            </div>

            <div className='relative w-[35%]'>
              <AspectRatio ratio={137 / 48} className='relative'>
                <MediumDisabledButton className='h-full w-full' />
              </AspectRatio>
              <span className='font-bdstreet text-shadow-orange-btn absolute inset-0 flex items-center justify-center text-[20px] font-bold text-white'>
                Chia sẻ
              </span>
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}

export default ShareDialog
