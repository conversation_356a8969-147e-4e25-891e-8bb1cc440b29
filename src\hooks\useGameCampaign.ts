import { useMutation } from '@tanstack/react-query'
import axios from 'axios'

interface GameCampaignRequest {
  userId: string
  appVersion: string
  from: string
}

export interface GameCampaignResponse {
  _id: string
  avatar: string
  canSharePostToday: boolean
  furthestLevel: number
  isCheckInToday: boolean
  isFriendDoneTaskFirst: boolean
  isJoinedGame: boolean
  isPlayedGame: boolean
  numberOfTurn: number
  numberOfGift: number
}

export const useGameCampaign = () => {
  return useMutation({
    mutationFn: async (payload: GameCampaignRequest) => {
      const { data } = await axios.post('/api/gameCampaign', payload)
      return data
    }
  })
}
