// src/app/api/getAlertPopup/route.ts
import { NextResponse } from 'next/server'
import axios from 'axios'

export async function POST(req: Request) {
  const body = await req.json()

  try {
    const { data } = await axios.post('http://192.168.88.32:17100/api/v5/api-asker-vn/get-alert-popup', body, {
      headers: {
        accessKey: '****************************************************************',
        'Content-Type': 'application/json'
      }
    })

    return NextResponse.json(data)
  } catch (err: unknown) {
    if (err instanceof Error) {
      return NextResponse.json({ error: err.message }, { status: 500 })
    }
    return NextResponse.json({ error: 'Request failed' }, { status: 500 })
  }
}
