// src/components/loadingScreen/LoadingScreen.tsx
'use client'

import Image from 'next/image'
import React, { useEffect } from 'react'
import { AspectRatio } from '@/components/ui/aspect-ratio'
import ProgressBarSvg from '@/assets/loading/progressBar.svg'
import LoadingBar from '@/components/loadingScreen/LoadingBar'
import { useLanguageStore } from '@/stores/useLanguageStore'
import { useGameStore } from '@/stores/useGameStore'
import { useGameCampaign } from '@/hooks/useGameCampaign'

interface LoadingScreenProps {
  onProgress?: (value: number) => void
  onSuccess?: () => void
  onError?: () => void
}

const LoadingScreen: React.FC<LoadingScreenProps> = ({ onProgress, onSuccess, onError }) => {
  const { mutateAsync } = useGameCampaign()
  const setGameCampaign = useGameStore((state) => state.setGameCampaign)

  useEffect(() => {
    const fetchCampaign = async () => {
      try {
        const data = await mutateAsync({
          userId: 'x391d6b4ecadb6625af716fe675cc21cf',
          appVersion: '3.0.0',
          from: 'WEBVIEW'
        })
        setGameCampaign(data)
        onSuccess?.()
      } catch (err) {
        console.error('Failed to load campaign:', err)
        onError?.()
      }
    }
    fetchCampaign()
  }, [mutateAsync, setGameCampaign, onSuccess, onError])

  const language = useLanguageStore((state) => state.language)
  const titleSrc = {
    EN: '/loading/titleEN.png',
    KO: '/loading/titleKO.png',
    VN: '/loading/titleVN.png'
  }[language]
  const widthClass = language === 'VN' ? 'w-[80%]' : 'w-[57.5%]'

  return (
    <div className='relative h-[100dvh] w-[100dvw] overflow-hidden'>
      <Image src='/loading/loadingBG.webp' alt='Background' fill className='object-cover' priority quality={100} />
      <div
        className={`absolute top-[6%] left-1/2 flex h-[39%] -translate-x-1/2 items-center justify-center ${widthClass}`}
      >
        <AspectRatio ratio={1986 / 1170} className='h-auto w-full'>
          <Image src={titleSrc} alt={`Title ${language}`} fill className='object-contain' priority quality={100} />
        </AspectRatio>
      </div>
      <div className='absolute bottom-0 w-full translate-x-[2.5%]'>
        <AspectRatio ratio={372 / 155}>
          <div className='relative h-full w-full'>
            <ProgressBarSvg className='h-full w-full' />
            <LoadingBar onProgress={onProgress} />
          </div>
        </AspectRatio>
      </div>
    </div>
  )
}

export default LoadingScreen
