// src/components/dialog/WinDialog.tsx
'use client'

import { Dialog, DialogContent } from '@/components/ui/dialog'
import Image from 'next/image'
import { AspectRatio } from '@/components/ui/aspect-ratio'
import RoundIconButton from '@/components/common/RoundIconButton'
import HomeIcon from '@/assets/dialog/home.svg'
import ShareIcon from '@/assets/dialog/share.svg'
import RetryIcon from '@/assets/dialog/retry.svg'
import NextIcon from '@/assets/dialog/next.svg'
import { useForfeitSpin } from '@/hooks/useForfeitSpin'
import { useGameStore } from '@/stores/useGameStore'

interface WinDialogProps {
  open: boolean
  onClose: () => void
  onShare: () => void
  stage: number
  score: number
  onNavigate: (stage: number) => void
  variant?: 'home' | 'game'
}

export default function WinDialog({
  open,
  onClose,
  onShare,
  stage,
  score,
  onNavigate,
  variant = 'home'
}: WinDialogProps) {
  const { gameCampaignId } = useGameStore()
  const { mutateAsync: forfeitSpin } = useForfeitSpin()

  const backgroundImage = variant === 'game' ? '/dialog/winGameBG.png' : '/dialog/winHomeBG.png'
  const rewardText = variant === 'game' ? 'Chúc mừng bạn đã nhận được' : 'Bạn đã nhận được'
  const ActionIcon = variant === 'game' ? NextIcon : RetryIcon

  const handleActionClick = async () => {
    try {
      if (gameCampaignId) {
        if (variant === 'home') {
          // For home variant (retry), use RETRY action
          await forfeitSpin({
            action: 'RETRY',
            levelNumber: stage,
            gameCampaignId
          })
        } else if (variant === 'game') {
          // For game variant (next level), use FORFEIT_SPIN action for next level
          await forfeitSpin({
            action: 'FORFEIT_SPIN',
            levelNumber: stage + 1,
            gameCampaignId
          })
        }
      }
    } catch (error) {
      console.error('Failed to forfeit spin:', error)
      // Continue with navigation even if API call fails
    }

    onClose()
    onNavigate(stage)
  }

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent showCloseButton={false} className='border-none bg-transparent p-0 shadow-none'>
        <AspectRatio ratio={1560 / 2720} className='relative'>
          <Image src={backgroundImage} alt='Win' fill quality={100} />

          {/* Title */}
          <h2 className='font-signwriter absolute top-[26%] left-1/2 w-[35%] -translate-x-1/2 text-center text-[24px] text-[#FFFF70]'>
            Màn {stage}
          </h2>

          {/* Score */}
          <div className='font-montserrat absolute top-[41%] left-1/2 flex h-[29%] w-[65%] -translate-x-1/2 flex-col text-center'>
            <p className='mt-[5%] text-[10px] font-extrabold text-white'>Điểm</p>
            <p className='score-shadow -translate-y-[25%] text-[33px] font-extrabold text-[#FFFF70]'>{score}</p>
            <p className='my-[4%] text-[12px] font-medium text-white'>{rewardText}</p>
            <div className='w-full'>
              <AspectRatio ratio={996 / 224}>
                <div className='relative flex h-full w-full'>
                  {/* Background image */}
                  <Image src='/dialog/voucherBG.png' alt='Reward' fill quality={100} />

                  {/* Content wrapper */}
                  <div className='absolute inset-0 flex'>
                    {/* Left 20% */}
                    <div className='flex h-full w-[22.5%] items-center justify-center'>
                      <AspectRatio ratio={1}>
                        <Image src='/dialog/logo.png' alt='Logo' fill className='p-[15%]' quality={100} />
                      </AspectRatio>
                    </div>

                    {/* Right 80% */}
                    <div className='flex h-full w-[77.5%] items-center bg-gradient-to-b from-[#FFFF70] to-[#FF8228] to-50% bg-clip-text px-[4%] text-start text-[14px] font-bold text-transparent'>
                      Voucher giảm giá 20.000đ của bTaskee
                    </div>
                  </div>
                </div>
              </AspectRatio>
            </div>
          </div>

          {/* Round Buttons */}
          <div className='absolute top-[69.5%] left-1/2 flex h-[7.5%] w-[47.5%] -translate-x-1/2 items-center justify-between'>
            <RoundIconButton
              className='w-[10.6dvw]'
              icon={<ShareIcon className='z-20 h-[50%] w-[50%]' />}
              onClick={() => {
                onClose()
                onShare()
              }}
            />
            <RoundIconButton
              className='w-[10.6dvw]'
              icon={<HomeIcon className='z-20 h-[50%] w-[50%]' />}
              onClick={onClose}
            />
            <RoundIconButton
              className='w-[10.6dvw]'
              icon={<ActionIcon className='z-20 h-[50%] w-[50%]' />}
              onClick={handleActionClick}
            />
          </div>
        </AspectRatio>
      </DialogContent>
    </Dialog>
  )
}
