import { create } from 'zustand'
import { GameCampaignResponse } from '@/hooks/useGameCampaign'

interface GameState {
  gameCampaignId: string | null
  avatar: string | null
  canSharePostToday: boolean
  isFriendDoneTaskFirst: boolean
  isJoinedGame: boolean
  isPlayedGame: boolean
  currentLevel: number
  turns: number
  gifts: number
  hasLoggedInToday: boolean

  setCurrentLevel: (level: number) => void
  setTurns: (turns: number) => void
  setGifts: (gifts: number) => void
  setHasLoggedInToday: (value: boolean) => void
  setGameCampaign: (payload: GameCampaignResponse) => void
}

export const useGameStore = create<GameState>((set) => ({
  gameCampaignId: null,
  avatar: null,
  canSharePostToday: false,
  isFriendDoneTaskFirst: false,
  isJoinedGame: false,
  isPlayedGame: false,
  currentLevel: 20,
  turns: 10,
  gifts: 0,
  hasLoggedInToday: false,

  setCurrentLevel: (level) => set({ currentLevel: level }),
  setTurns: (turns) => set({ turns }),
  setGifts: (gifts) => set({ gifts }),
  setHasLoggedInToday: (value) => set({ hasLoggedInToday: value }),
  setGameCampaign: (payload) =>
    set({
      gameCampaignId: payload._id,
      avatar: payload.avatar,
      canSharePostToday: payload.canSharePostToday,
      currentLevel: payload.furthestLevel,
      hasLoggedInToday: payload.isCheckInToday,
      isFriendDoneTaskFirst: payload.isFriendDoneTaskFirst,
      isJoinedGame: payload.isJoinedGame,
      isPlayedGame: payload.isPlayedGame,
      turns: payload.numberOfTurn,
      gifts: payload.numberOfGift
    })
}))
