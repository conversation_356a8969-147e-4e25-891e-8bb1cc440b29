// src/stores/useGameStore.ts
import { create } from 'zustand'
import { GameCampaignResponse } from '@/hooks/useGameCampaign'

interface PassedLevel {
  level: number
  score: number
}

interface GameState {
  gameCampaignId: string | null
  avatar: string | null
  canSharePostToday: boolean
  isFriendDoneTaskFirst: boolean
  isJoinedGame: boolean
  isPlayedGame: boolean
  currentLevel: number
  turns: number
  gifts: number
  hasLoggedInToday: boolean
  username: string | null
  passedLevels: PassedLevel[]

  setCurrentLevel: (level: number) => void
  setTurns: (turns: number) => void
  setGifts: (gifts: number) => void
  setHasLoggedInToday: (value: boolean) => void
  setGameCampaign: (payload: GameCampaignResponse) => void
}

export const useGameStore = create<GameState>((set) => ({
  gameCampaignId: null,
  avatar: null,
  canSharePostToday: false,
  isFriendDoneTaskFirst: false,
  isJoinedGame: false,
  isPlayedGame: false,
  currentLevel: 0,
  turns: 0,
  gifts: 0,
  hasLoggedInToday: false,
  username: null,
  passedLevels: [],

  setCurrentLevel: (level) => set({ currentLevel: level }),
  setTurns: (turns) => set({ turns }),
  setGifts: (gifts) => set({ gifts }),
  setHasLoggedInToday: (value) => set({ hasLoggedInToday: value }),
  setGameCampaign: (payload) =>
    set({
      gameCampaignId: payload._id,
      avatar: payload.avatar,
      canSharePostToday: payload.checkListMission.canSharePostToday,
      hasLoggedInToday: payload.checkListMission.isCheckInToday,
      isFriendDoneTaskFirst: payload.checkListMission.isFriendDoneTaskFirst,
      isJoinedGame: payload.checkListMission.isJoinFirstGame,
      isPlayedGame: payload.isPlayedGame,
      currentLevel: payload.furthestLevel,
      turns: payload.numberOfTurn,
      gifts: payload.numberOfGift,
      username: payload.username,
      passedLevels: payload.infoLevels.map((lvl) => ({
        level: lvl.level,
        score: lvl.score
      }))
    })
}))
