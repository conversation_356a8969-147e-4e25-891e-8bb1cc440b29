'use client'

import { <PERSON>er, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>er, <PERSON>er<PERSON><PERSON><PERSON>, Drawer<PERSON>lose, DrawerFooter } from '@/components/ui/drawer'
import { AspectRatio } from '@/components/ui/aspect-ratio'
import { Avatar, AvatarImage, AvatarFallback } from '@/components/ui/avatar'
import { Separator } from '@/components/ui/separator'
import MediumerOrangeButton from '@/assets/drawer/mediumerOrangeButton.svg'
import MediumGreenButton from '@/assets/drawer/mediumGreenButton.svg'
import EmptyLeaderboard from '@/assets/drawer/empty_leaderboard.svg'
import Image from 'next/image'

interface LeaderboardDrawerProps {
  open: boolean
  onClose: () => void
}

interface LeaderboardEntry {
  id: number
  name: string
  score: number
  avatar: string
}

const dummyLeaderboard: LeaderboardEntry[] = [
  { id: 1, name: '<PERSON>', score: 8456, avatar: 'https://i1.sndcdn.com/avatars-000191448961-vwi4k6-t240x240.jpg' },
  { id: 2, name: '<PERSON><PERSON><PERSON><PERSON> <PERSON>', score: 8456, avatar: 'https://cf.shopee.vn/file/f66a6c09eec5298af5ca3f09f87ed279' },
  {
    id: 3,
    name: 'Minh Hiếu',
    score: 8456,
    avatar:
      'https://danviet-24h.ex-cdn.com/files/upload/2-2020/images/2020-05-12/83395730_134841691333804_1955901610945150976_o-1589260776-4-width960height960.jpg'
  },
  {
    id: 4,
    name: 'Phan Kim Tiền',
    score: 3111,
    avatar: 'https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcT2HzojwlBpo-h3vaRPFSLRYNkKoSiKUa-B1Q&s'
  },
  { id: 5, name: 'Cậu ba Kenji', score: 3111, avatar: 'https://github.com/shadcn.png' },
  {
    id: 7749,
    name: 'Cô Hai Báo',
    score: 69,
    avatar:
      'https://i.vgt.vn/2024/1/14/co-hai-bao-khien-nhieu-nguoi-quay-xe-tu-tong-tai-hoa-thu-ky-kim-xinh-dep-321-7074278.png'
  }
]

const LeaderboardDrawer: React.FC<LeaderboardDrawerProps> = ({ open, onClose }) => {
  // sort for safety so index -> rank (highest score = rank 1)
  const sorted = [...dummyLeaderboard].sort((a, b) => b.score - a.score)

  const top = sorted[0] ?? { id: 0, name: '', score: 0, avatar: '' }
  const second = sorted[1] ?? { id: 0, name: '', score: 0, avatar: '' }
  const third = sorted[2] ?? { id: 0, name: '', score: 0, avatar: '' }
  const fourth = sorted[3] ?? { id: 0, name: '', score: 0, avatar: '' }
  const fifth = sorted[4] ?? { id: 0, name: '', score: 0, avatar: '' }
  const sixth = sorted[5] ?? { id: 0, name: '', score: 0, avatar: '' }

  const hasData = sorted.length > 0

  return (
    <Drawer open={open} onOpenChange={onClose}>
      <DrawerContent className='flex h-[97.5dvh] flex-col overflow-hidden'>
        {/* Fixed header */}
        <DrawerHeader className='h-[18.5%]'>
          <DrawerTitle className='font-bdstreet text-border-orange text-[32px]'>Bảng xếp hạng</DrawerTitle>
        </DrawerHeader>

        <div className='font-montserrat flex h-[70.5%] flex-col overflow-hidden'>
          {!hasData ? (
            <div className='flex flex-1 flex-col items-center justify-center'>
              <div className='flex w-[50%] flex-col items-center space-y-[20%]'>
                <AspectRatio ratio={1}>
                  <EmptyLeaderboard />
                </AspectRatio>
                <p className='text-center text-[14px] font-medium text-white'>Danh sách trống</p>
              </div>
            </div>
          ) : (
            <>
              {/* Top div - 43% height */}
              <div className='flex h-[43%] flex-col'>
                <div className='flex h-full flex-row justify-between'>
                  {/* Column 1 - rank 2 */}
                  <div className='relative flex h-full w-1/3 items-end justify-end pr-[2.5%]'>
                    <div className='relative aspect-[78/133] h-[60%]'>
                      <div className='z-0 mx-auto aspect-square w-[90%] rounded-full bg-gradient-to-b from-[#FFFFFF] to-[#C9C9C9] p-[3%]'>
                        <AspectRatio ratio={1}>
                          <Avatar className='h-full w-full'>
                            <AvatarImage src={second.avatar} alt={second.name} />
                            <AvatarFallback>{(second.name || 'U').charAt(0)}</AvatarFallback>
                          </Avatar>
                        </AspectRatio>
                      </div>
                      <div className='absolute top-[52.5%] left-1/2 z-10 flex h-5 w-5 -translate-x-1/2 -translate-y-1/2 items-center justify-center rounded-full bg-[#FFFFFF]'>
                        <span className='translate-y-0.25 text-[16px] font-bold text-black'>2</span>
                      </div>
                      <div className='absolute bottom-[2%] left-1/2 flex w-full -translate-x-1/2 flex-col items-center'>
                        <span className='text-[16px] font-bold text-[#FFFFFF]'>{second.name}</span>
                        <span className='text-[14px] font-medium text-white'>{second.score.toLocaleString()}</span>
                      </div>
                    </div>
                  </div>

                  {/* Column 2 - rank 1 */}
                  <div className='relative flex h-full w-1/3 flex-col items-center justify-center'>
                    <div className='relative flex aspect-[104/203] h-[90%] items-center justify-center'>
                      <div className='absolute top-[5%] left-1/2 z-10 w-[55%] -translate-x-1/2'>
                        <AspectRatio ratio={221 / 207}>
                          <Image src='/drawer/crown.png' alt='Crown' fill quality={100} />
                        </AspectRatio>
                      </div>
                      <div className='z-0 aspect-square w-full rounded-full bg-gradient-to-b from-[#FFFF70] to-[#F6B936] p-[3%]'>
                        <AspectRatio ratio={1}>
                          <Avatar className='h-full w-full'>
                            <AvatarImage src={top.avatar} alt={top.name} />
                            <AvatarFallback>{(top.name || 'U').charAt(0)}</AvatarFallback>
                          </Avatar>
                        </AspectRatio>
                      </div>
                      <div className='absolute bottom-[13.5%] left-1/2 z-10 flex h-5 w-5 -translate-x-1/2 -translate-y-1/2 items-center justify-center rounded-full bg-[#FFFF70]'>
                        <span className='translate-y-0.25 text-[16px] font-bold text-black'>1</span>
                      </div>
                      <div className='absolute -bottom-[5%] flex w-full flex-col items-center'>
                        <span className='bg-gradient-to-b from-[#FFFF70] to-[#F6B936] bg-clip-text text-[16px] font-bold text-transparent'>
                          {top.name}
                        </span>
                        <span className='text-[14px] font-medium text-white'>{top.score.toLocaleString()}</span>
                      </div>
                    </div>
                  </div>

                  {/* Column 3 - rank 3 */}
                  <div className='relative flex h-full w-1/3 items-end justify-start pl-[2.5%]'>
                    <div className='relative aspect-[78/133] h-[60%]'>
                      <div className='z-0 mx-auto aspect-square w-[90%] rounded-full bg-gradient-to-b from-[#E0AE78] to-[#CC863A] p-[3%]'>
                        <AspectRatio ratio={1}>
                          <Avatar className='h-full w-full'>
                            <AvatarImage src={third.avatar} alt={third.name} />
                            <AvatarFallback>{(third.name || 'U').charAt(0)}</AvatarFallback>
                          </Avatar>
                        </AspectRatio>
                      </div>
                      <div className='absolute top-[52.5%] left-1/2 z-10 flex h-5 w-5 -translate-x-1/2 -translate-y-1/2 items-center justify-center rounded-full bg-[#F6B936]'>
                        <span className='translate-y-0.25 text-[16px] font-bold text-black'>3</span>
                      </div>
                      <div className='absolute bottom-[2%] left-1/2 flex w-[150%] -translate-x-1/2 flex-col items-center'>
                        <span className='line-clamp-1 text-[16px] font-bold text-[#FFFFFF]'>{third.name}</span>
                        <span className='text-[14px] font-medium text-white'>{third.score.toLocaleString()}</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Bottom div - preserved layout */}
              <div className='flex flex-1 flex-col items-center justify-end space-y-[4%]'>
                {/* Row 1 - rank 4 */}
                <div className='relative flex h-[26.7%] w-[91.9%] flex-col justify-end'>
                  <div className='absolute top-0 left-[10%] z-10 aspect-square h-[75%] rounded-full'>
                    <AspectRatio ratio={1}>
                      <Avatar className='h-full w-full'>
                        <AvatarImage src={fourth.avatar} alt={fourth.name} />
                        <AvatarFallback>{(fourth.name || 'U').charAt(0)}</AvatarFallback>
                      </Avatar>
                    </AspectRatio>
                    <div className='absolute -bottom-[15%] left-1/2 z-10 flex h-5 w-5 -translate-x-1/2 items-center justify-center rounded-full bg-[#F7D6A1]'>
                      <span className='translate-y-0.25 text-[16px] font-bold text-black'>4</span>
                    </div>
                  </div>

                  <div className='flex h-[85%] w-full flex-col justify-center rounded-3xl border border-white/30 bg-white/5 pl-[30%] text-white backdrop-blur-xs'>
                    <span className='bg-gradient-to-b from-[#FFFF70] to-[#F6B936] bg-clip-text text-[16px] font-bold text-transparent'>
                      {fourth.name}
                    </span>
                    <span className='text-[14px] font-medium'>{fourth.score.toLocaleString()} điểm</span>
                  </div>
                </div>

                {/* Row 2 - rank 5 */}
                <div className='relative flex h-[26.7%] w-[91.9%] flex-col justify-end'>
                  <div className='absolute top-0 left-[10%] z-10 aspect-square h-[75%] rounded-full'>
                    <AspectRatio ratio={1}>
                      <Avatar className='h-full w-full'>
                        <AvatarImage src={fifth.avatar} alt={fifth.name} />
                        <AvatarFallback>{(fifth.name || 'U').charAt(0)}</AvatarFallback>
                      </Avatar>
                    </AspectRatio>
                    <div className='absolute -bottom-[15%] left-1/2 z-10 flex h-5 w-5 -translate-x-1/2 items-center justify-center rounded-full bg-[#F7D6A1]'>
                      <span className='translate-y-0.25 text-[16px] font-bold text-black'>5</span>
                    </div>
                  </div>

                  <div className='flex h-[85%] w-full flex-col justify-center rounded-3xl border border-white/30 bg-white/5 pl-[30%] text-white backdrop-blur-xs'>
                    <span className='bg-gradient-to-b from-[#FFFF70] to-[#F6B936] bg-clip-text text-[16px] font-bold text-transparent'>
                      {fifth.name}
                    </span>
                    <span className='text-[14px] font-medium'>{fifth.score.toLocaleString()} điểm</span>
                  </div>
                </div>

                {/* Separator (preserve original position and style) */}
                <Separator className='w-[91.9%] bg-[#535548]' />

                {/* Row 3 - rank 6 */}
                <div className='relative flex h-[26.7%] w-[91.9%] flex-col justify-end'>
                  <div className='absolute top-0 left-[10%] z-10 aspect-square h-[75%] rounded-full'>
                    <AspectRatio ratio={1}>
                      <Avatar className='h-full w-full'>
                        <AvatarImage src={sixth.avatar} alt={sixth.name} />
                        <AvatarFallback>{(sixth.name || 'U').charAt(0)}</AvatarFallback>
                      </Avatar>
                    </AspectRatio>
                    <div className='absolute -bottom-[17.5%] left-1/2 z-10 flex -translate-x-1/2 items-center justify-center'>
                      <span className='text-[16px] font-bold text-white'>{sixth.id}</span>
                    </div>
                  </div>

                  <div className='flex h-[85%] w-full flex-col justify-center rounded-3xl border border-white/30 bg-white/5 pl-[30%] text-white backdrop-blur-xs'>
                    <span className='text-[16px] font-bold text-[#FF5500]'>{sixth.name}</span>
                    <span className='text-[14px] font-medium'>{sixth.score.toLocaleString()} điểm</span>
                  </div>
                </div>
              </div>
            </>
          )}
        </div>

        {/* Footer */}
        <DrawerFooter className='flex h-[11%] items-center justify-center px-[5%] pb-[5%]'>
          <div className='flex w-full justify-center gap-[7.5%]'>
            <DrawerClose className='w-[37.5%]'>
              <AspectRatio ratio={137 / 48} className='relative w-full'>
                <MediumerOrangeButton className='absolute inset-0 h-full w-full' />
                <span className='font-bdstreet text-shadow-orange-btn absolute inset-0 flex items-center justify-center text-[20px] text-white'>
                  Đóng
                </span>
              </AspectRatio>
            </DrawerClose>
            <div className='w-[37.5%]'>
              <AspectRatio ratio={137 / 48} className='relative w-full cursor-pointer'>
                <MediumGreenButton className='absolute inset-0 h-full w-full' />
                <span className='font-bdstreet text-shadow-orange-btn absolute inset-0 flex items-center justify-center text-[20px] text-white'>
                  Chia sẻ
                </span>
              </AspectRatio>
            </div>
          </div>
        </DrawerFooter>
      </DrawerContent>
    </Drawer>
  )
}

export default LeaderboardDrawer
