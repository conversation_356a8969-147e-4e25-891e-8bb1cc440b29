// src/components/gameScreen/TimeUpDialog.tsx
'use client'

import Image from 'next/image'
import { AspectRatio } from '@/components/ui/aspect-ratio'
import { Dialog, DialogContent } from '@/components/ui/dialog'
import RoundIconButton from '@/components/common/RoundIconButton'
import HomeIcon from '@/assets/dialog/home.svg'
import RetryIcon from '@/assets/dialog/retry.svg'
import { useGameStore } from '@/stores/useGameStore'
import { useForfeitSpin } from '@/hooks/useForfeitSpin'

interface TimeUpDialogProps {
  open: boolean
  onRestart: () => void
  onQuit: () => void
  stage: number
}

export default function TimeUpDialog({ open, stage, onRestart, onQuit }: TimeUpDialogProps) {
  const { turns, gameCampaignId } = useGameStore()
  const { mutateAsync: forfeitSpin } = useForfeitSpin()

  const handleRetry = async () => {
    try {
      if (gameCampaignId) {
        await forfeitSpin({
          action: 'RETRY',
          levelNumber: stage,
          gameCampaignId
        })
      }
    } catch (error) {
      console.error('Failed to forfeit spin:', error)
      // Continue with restart even if API call fails
    }

    onRestart()
  }
  return (
    <Dialog open={open}>
      <DialogContent showCloseButton={false} className='border-none bg-transparent p-0 shadow-none'>
        <AspectRatio ratio={1560 / 2720} className='relative'>
          <Image src='/dialog/outOfTimeBG.png' alt='Time up background' fill quality={100} />
          {/* Centered content */}
          <div className='relative top-[41%] flex h-full w-full justify-center'>
            <div className='flex h-[25%] w-1/2 flex-col items-center justify-center gap-[15%]'>
              <h2 className='font-signwriter text-[32px] text-[#FFFF70]'>Màn {stage}</h2>
              <div className='flex flex-col items-center justify-center gap-[2%]'>
                <p className='font-montserrat text-[12px] font-medium text-white'>Tiếc quá!</p>
                <p className='font-montserrat text-[12px] font-medium text-white'>Hết thời gian mất rồi.</p>
                <p className='font-montserrat text-[12px] font-medium text-white'>Cố gắng lần sau nhé!</p>
              </div>
            </div>
          </div>
          {/* Absolute blue div with buttons */}
          <div
            className={`absolute top-[69.5%] left-1/2 flex h-[7.5%] w-[26%] -translate-x-1/2 items-center ${
              turns === 0 ? 'justify-center' : 'justify-between'
            }`}
          >
            <RoundIconButton
              className='w-[10.6dvw]'
              icon={<HomeIcon className='z-20 h-[50%] w-[50%]' />}
              onClick={onQuit}
            />
            {turns > 0 && (
              <RoundIconButton
                className='w-[10.6dvw]'
                icon={<RetryIcon className='z-20 h-[50%] w-[50%]' />}
                onClick={handleRetry}
              />
            )}
          </div>
          <div className='font-bdstreet absolute bottom-[5%] left-1/2 flex h-[7.5%] w-[29%] -translate-x-1/2 items-center justify-between'>
            <AspectRatio ratio={363 / 173}>
              <div
                className='relative flex h-full w-full cursor-pointer items-center justify-center'
                onClick={() => console.log('User opens Turns')}
              >
                <Image src='/home/<USER>' alt='Turns Button' fill />
                <span className='absolute z-20 ml-[30%] text-[18.54px] text-[#FF5500]'>{turns}</span>
              </div>
            </AspectRatio>
          </div>
        </AspectRatio>
      </DialogContent>
    </Dialog>
  )
}
