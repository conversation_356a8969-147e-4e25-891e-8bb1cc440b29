import { NextRequest, NextResponse } from 'next/server'
import axios, { AxiosError } from 'axios'
import { GameCampaignResponse } from '@/hooks/useGameCampaign'

export async function POST(req: NextRequest) {
  try {
    const body = await req.json()
    console.log('Incoming Request Body:', body)

    const { data } = await axios.post<GameCampaignResponse>(
      'http://192.168.88.32:17100/api/v3/api-asker-vn/get-running-game-campaign',
      body,
      {
        headers: {
          accessKey: '****************************************************************',
          'Content-Type': 'application/json'
        }
      }
    )

    console.log('Outgoing Response Data:', data)
    return NextResponse.json(data)
  } catch (error) {
    const err = error as AxiosError
    console.error('Error while handling request:', {
      message: err.message,
      response: err.response?.data,
      status: err.response?.status
    })

    return NextResponse.json({ error: err.message }, { status: 500 })
  }
}
